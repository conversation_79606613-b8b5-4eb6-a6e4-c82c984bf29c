plugins {
    id 'java'
    id 'application'
    id 'org.javamodularity.moduleplugin' version '1.8.12'
    id 'org.openjfx.javafxplugin' version '0.1.0'
    id 'org.beryx.jlink' version '3.0.1'
    id 'com.github.johnrengelman.shadow' version '8.1.1'
}

group = 'com.logictrue'
version = '1.0'

repositories {
    mavenLocal()
    maven { url = "https://maven.aliyun.com/repository/public" }
    // 阿里云 Gradle 插件仓库
    maven { url = "https://maven.aliyun.com/repository/gradle-plugin" }
    mavenCentral()
}

ext {
    junitVersion = '5.10.0'
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
    options.compilerArgs += [
            "--add-reads",
            "com.logictrue=ALL-UNNAMED"
    ]
}


application {
    mainModule = 'com.logictrue'
    mainClass = 'com.logictrue.App'
}

javafx {
    version = '21'
    modules = ['javafx.controls', 'javafx.fxml']
}

dependencies {
    implementation("com.fasterxml.jackson.core:jackson-databind:2.17.2")
    implementation("org.slf4j:slf4j-api:2.0.9")
    implementation("ch.qos.logback:logback-classic:1.4.14")
    implementation("com.logictrue:logictrue-iot-excel:1.1")

    // 使用较稳定的XMLBeans版本
    implementation("org.apache.xmlbeans:xmlbeans:5.1.1")
    implementation("jakarta.ws.rs:jakarta.ws.rs-api:3.1.0")

    // 显式添加fastjson2依赖以解决版本冲突
    implementation("com.alibaba.fastjson2:fastjson2:2.0.58")

    // MyBatis-Plus 核心依赖
    implementation("com.baomidou:mybatis-plus:3.4.1")
    implementation("com.baomidou:mybatis-plus-annotation:3.4.1")

    // SQLite数据库
    implementation("org.xerial:sqlite-jdbc:3.42.0.0")

    // 数据库连接池
    implementation("com.zaxxer:HikariCP:5.0.1")

    testImplementation("org.junit.jupiter:junit-jupiter-api:${junitVersion}")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine:${junitVersion}")
}

test {
    useJUnitPlatform()
}


//jar {
//    String someString = ''
//    configurations.runtime.each {someString = someString + " lib//"+it.name}
//    manifest {
//        attributes 'Main-Class': 'com.it.messtart.MesStartApplication'
//        attributes 'Class-Path': someString
//    }
//
//}

//jar {
//    manifest {
//        attributes(
//                'Main-Class': 'com.it.messtart.MesStartApplication'
//        )
//    }
//    from {
//        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
//    }
//    exclude 'META-INF/*.RSA', 'META-INF/*.SF', 'META-INF/*.DSA'
//}

//tasks.named('compileJava') {
//    options.compilerArgs += '--module-path'
//    options.compilerArgs += files(classpath).asType(List)
//    options.compilerArgs += '--add-modules'
//    options.compilerArgs += 'javafx.controls,javafx.fxml'
//}

jlink {
    imageZip = project.file("${layout.buildDirectory.get()}/distributions/app-${javafx.platform.classifier}.zip")
    options = ['--strip-debug', '--compress', 'zip-6', '--no-header-files', '--no-man-pages']

    // 强制模块路径，忽略自动模块, 强制合并以解决模块化问题
    forceMerge('poi.ooxml')
    forceMerge('jackson.databind')
    forceMerge('jackson.core')
    forceMerge('jackson.annotations')
    forceMerge('logback.classic')
    forceMerge('logback.core')
    forceMerge('slf4j.api')
    forceMerge('xmlbeans')
    forceMerge('fastjson2')
    forceMerge('mybatis.plus')
    forceMerge('sqlite.jdbc')
    forceMerge('org.xerial.sqlitejdbc')
    forceMerge('HikariCP')

    launcher {
        name = 'app'
    }
    jpackage {
        imageName = 'IotClient'
        installerName = 'IotClient'

        // 根据操作系统选择不同的配置
        if (org.gradle.internal.os.OperatingSystem.current().isWindows()) {
            installerType = 'exe'
            installerOptions = [
                '--win-shortcut',
                '--win-menu',
                '--win-dir-chooser',
                '--win-per-user-install',
                '--win-upgrade-uuid', 'e55e80e0-597e-4a0b-ae77-e06117109143'
            ]
        } else if (org.gradle.internal.os.OperatingSystem.current().isLinux()) {
            // 可以通过项目属性来选择安装包类型
            // ./gradlew jpackage -PinstallerType=deb
            // ./gradlew jpackage -PinstallerType=rpm
            // ./gradlew jpackage -PinstallerType=app-image
            def linuxInstallerType = project.findProperty('installerType') ?: 'deb'
            installerType = linuxInstallerType

            if (linuxInstallerType == 'app-image') {
                // app-image 类型不需要安装选项，只创建应用程序目录
                installerOptions = []
                // 对于app-image类型，我们需要跳过安装包创建步骤
                skipInstaller = true
            } else {
                // deb 和 rpm 包的安装选项
                installerOptions = [
                    '--linux-package-name', 'iot-client',
                    '--linux-menu-group', 'Development',
                    '--linux-app-category', 'Development',
                    '--linux-shortcut'
                ]

                // 只有 deb 包需要维护者信息
                if (linuxInstallerType == 'deb') {
                    installerOptions += ['--linux-deb-maintainer', '<EMAIL>']
                }

                // 只有 rpm 包需要许可证类型
                if (linuxInstallerType == 'rpm') {
                    installerOptions += ['--linux-rpm-license-type', 'MIT']
                }
            }
        } else if (org.gradle.internal.os.OperatingSystem.current().isMacOsX()) {
            installerType = 'dmg'
            installerOptions = [
                '--mac-package-name', 'IotClient',
                '--mac-package-identifier', 'com.logictrue.iotclient'
            ]
        }
    }
}

jlinkZip {
    group = 'distribution'
}

shadowJar {
    archiveBaseName.set('IotClient')
    archiveClassifier.set('')
    archiveVersion.set('')
    manifest {
        attributes 'Main-Class': 'com.logictrue.App'
    }
}

// 创建AppImage的自定义任务
tasks.register('createAppImage') {
    dependsOn 'jpackageImage'
    group = 'distribution'
    description = '创建AppImage格式的单文件应用程序'

    doLast {
        def appImageDir = file("${layout.buildDirectory.get()}/appimage")
        def sourceDir = file("${layout.buildDirectory.get()}/jpackage/IotClient")
        def appImageFile = file("${layout.buildDirectory.get()}/distributions/IotClient-${version}.AppImage")

        // 检查源目录是否存在
        if (!sourceDir.exists()) {
            throw new GradleException("Source directory not found: ${sourceDir}. Please run 'jpackageImage' task first.")
        }

        // 清理并创建AppImage目录结构
        delete appImageDir
        appImageDir.mkdirs()

        // 复制jpackage生成的应用程序目录到AppImage目录
        copy {
            from sourceDir
            into appImageDir
        }

        // 创建.desktop文件
        def desktopFile = file("${appImageDir}/IotClient.desktop")
        desktopFile.text = """[Desktop Entry]
Type=Application
Name=IoT数据采集系统
Comment=IoT数据采集客户端应用程序
Exec=IotClient
Icon=IotClient
Categories=Development;Utility;
Terminal=false
StartupWMClass=IotClient
"""

        // 创建AppRun脚本
        def appRunFile = file("${appImageDir}/AppRun")
        appRunFile.text = """#!/bin/bash
HERE="\$(dirname "\$(readlink -f "\${0}")")"
export PATH="\${HERE}/bin:\${PATH}"
export LD_LIBRARY_PATH="\${HERE}/lib:\${LD_LIBRARY_PATH}"
cd "\${HERE}"
exec "\${HERE}/bin/IotClient" "\$@"
"""
        appRunFile.setExecutable(true)

        // 复制图标文件（如果存在）
        def iconSource = file("${sourceDir}/lib/IotClient.png")
        if (iconSource.exists()) {
            copy {
                from iconSource
                into appImageDir
                rename { 'IotClient.png' }
            }
        } else {
            // 如果没有图标，创建一个符号链接到desktop文件中指定的图标
            println "Warning: Icon file not found at ${iconSource}"
        }

        // 使用appimagetool创建AppImage
        def appimagetool = file("${projectDir}/appimagetool-x86_64.AppImage")
        if (!appimagetool.exists()) {
            throw new GradleException("appimagetool-x86_64.AppImage not found in project root. Please download it from https://github.com/AppImage/AppImageKit/releases")
        }

        // 确保输出目录存在
        appImageFile.parentFile.mkdirs()

        // 执行appimagetool
        providers.exec {
            commandLine appimagetool.absolutePath, appImageDir.absolutePath, appImageFile.absolutePath
            environment 'ARCH', 'x86_64'
        }

        println "✅ AppImage created successfully!"
        println "📁 Location: ${appImageFile.absolutePath}"
        println "📏 Size: ${String.format('%.1f MB', appImageFile.length() / 1024.0 / 1024.0)}"
        println "🚀 Run with: ./${appImageFile.name}"
    }
}

// 便捷任务：创建所有分发格式
tasks.register('packageAll') {
    group = 'distribution'
    description = '创建所有支持的分发格式 (app-image, deb, AppImage)'

    dependsOn 'clean'

    doLast {
        println "开始创建所有分发格式..."
    }
}

// 为packageAll任务添加依赖关系
tasks.register('packageAppImage', GradleBuild) {
    tasks = ['jpackage', 'createAppImage']
    startParameter.projectProperties = ['installerType': 'app-image']
}

tasks.register('packageDeb', GradleBuild) {
    tasks = ['jpackage']
    startParameter.projectProperties = ['installerType': 'deb']
}

tasks.register('packageRpm', GradleBuild) {
    tasks = ['jpackage']
    startParameter.projectProperties = ['installerType': 'rpm']
}

packageAll.dependsOn packageAppImage, packageDeb, packageRpm
